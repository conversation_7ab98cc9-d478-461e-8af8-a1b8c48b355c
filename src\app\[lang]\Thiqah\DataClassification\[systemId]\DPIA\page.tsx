"use client";

import React, { useState, useEffect, useCallback, useMemo } from "react";
import { Locale } from "@/i18n-config";
import { useRouter } from "next/navigation";
import { ArrowLeft, ArrowRight, FileCheck, CheckCircle, User, Building, DollarSign, Scale, Database, Table, Eye, BarChart3, ChevronLeft, ChevronRight, Download } from "lucide-react";
import { Button } from "@/components/ui/button";
import { SystemsService, System, DPIAAssessment, SystemData } from "@/Firebase/firestore/SystemsService";
import { useToast } from "@/components/ui/use-toast";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";


interface DPIAPageProps {
  params: Promise<{ lang: Locale; systemId: string }>;
}

// Risk Level Cell Component
const RiskLevelCell = React.memo(({
  riskLevel,
  assessment,
  onCellClick,
  isRTL
}: {
  riskLevel?: string;
  assessment?: DPIAAssessment;
  onCellClick: () => void;
  isRTL: boolean;
}) => {
  if (!assessment || !riskLevel) {
    return (
      <div className="w-14 h-8 bg-gray-50 rounded-md mx-auto flex items-center justify-center border border-gray-200">
        <span className="text-xs text-gray-400 font-medium">
          {isRTL ? "—" : "—"}
        </span>
      </div>
    );
  }

  const getRiskConfig = (level: string) => {
    switch (level) {
      case 'HIGH':
        return {
          bg: 'bg-red-500',
          border: 'border-red-500',
          text: 'text-white',
          label: isRTL ? 'عالي' : 'HIGH',
          shadow: 'shadow-sm'
        };
      case 'MEDIUM':
        return {
          bg: 'bg-yellow-500',
          border: 'border-yellow-500',
          text: 'text-white',
          label: isRTL ? 'متوسط' : 'MED',
          shadow: 'shadow-sm'
        };
      case 'LOW':
        return {
          bg: 'bg-green-500',
          border: 'border-green-500',
          text: 'text-white',
          label: isRTL ? 'منخفض' : 'LOW',
          shadow: 'shadow-sm'
        };
      default:
        return {
          bg: 'bg-gray-50',
          border: 'border-gray-200',
          text: 'text-gray-500',
          label: isRTL ? 'غير محدد' : 'N/A',
          shadow: 'shadow-sm'
        };
    }
  };

  const config = getRiskConfig(riskLevel);

  return (
    <div
      className={`w-14 h-8 ${config.bg} ${config.border} rounded-md mx-auto flex items-center justify-center border cursor-pointer hover:opacity-80 transition-all duration-200 ${config.shadow}`}
      onClick={onCellClick}
      title={assessment ? (isRTL ? "انقر لعرض التفاصيل" : "Click to view details") : (isRTL ? "لم يتم التقييم" : "Not assessed")}
    >
      <span className={`text-xs font-semibold ${config.text} tracking-tight`}>
        {config.label}
      </span>
    </div>
  );
});

RiskLevelCell.displayName = 'RiskLevelCell';

// Memoized table row component for better performance
const MatrixRow = React.memo(({
  item,
  index,
  dpiaAssessments,
  onCellClick,
  isRTL
}: {
  item: SystemData;
  index: number;
  dpiaAssessments: DPIAAssessment[];
  onCellClick: (item: SystemData, domain: string) => void;
  isRTL: boolean;
}) => {
  // Find DPIA assessment for this item
  const assessment = dpiaAssessments.find(a => a.attributeId === item.id);

  // Helper function to get risk level from DPIA fields
  const getIndividualHarmRisk = () => {
    if (!assessment || !assessment.domainAnswers?.individualHarm?.riskLevel) return undefined;
    // Use the individual harm domain-specific risk level
    return assessment.domainAnswers.individualHarm.riskLevel;
  };

  const getBusinessReputationRisk = () => {
    if (!assessment || !assessment.domainAnswers?.businessReputation?.riskLevel) return undefined;
    // Use the business reputation domain-specific risk level
    return assessment.domainAnswers.businessReputation.riskLevel;
  };

  const getFinancialRisk = () => {
    if (!assessment || !assessment.domainAnswers?.financialImpact?.riskLevel) return undefined;
    // Use the financial impact domain-specific risk level
    return assessment.domainAnswers.financialImpact.riskLevel;
  };

  const getLegalRisk = () => {
    if (!assessment || !assessment.domainAnswers?.legalRegulatory?.riskLevel) return undefined;
    // Use the legal/regulatory domain-specific risk level
    return assessment.domainAnswers.legalRegulatory.riskLevel;
  };

  return (
    <tr key={item.id || index} className="hover:bg-gray-50">
      <td className="p-3 border-r border-gray-200">
        <div className="flex items-center gap-3">
          <div className={`w-3 h-3 rounded-full ${
            item.confidentialityLevel === "Top Secret" ? "bg-red-500" : "bg-orange-500"
          }`}></div>
          <div>
            <div className="font-medium text-gray-900">
              {item.tableName}
            </div>
            <div className="text-gray-600 text-sm">
              {item.columnName}
            </div>
          </div>
        </div>
      </td>
      <td className="p-3 text-center border-r border-gray-200">
        <RiskLevelCell
          riskLevel={getIndividualHarmRisk()}
          assessment={assessment}
          onCellClick={() => assessment && onCellClick(item, 'individualHarm')}
          isRTL={isRTL}
        />
      </td>
      <td className="p-3 text-center border-r border-gray-200">
        <RiskLevelCell
          riskLevel={getBusinessReputationRisk()}
          assessment={assessment}
          onCellClick={() => assessment && onCellClick(item, 'businessReputation')}
          isRTL={isRTL}
        />
      </td>
      <td className="p-3 text-center border-r border-gray-200">
        <RiskLevelCell
          riskLevel={getFinancialRisk()}
          assessment={assessment}
          onCellClick={() => assessment && onCellClick(item, 'financialConsequences')}
          isRTL={isRTL}
        />
      </td>
      <td className="p-3 text-center">
        <RiskLevelCell
          riskLevel={getLegalRisk()}
          assessment={assessment}
          onCellClick={() => assessment && onCellClick(item, 'legalRegulatory')}
          isRTL={isRTL}
        />
      </td>
    </tr>
  );
});

MatrixRow.displayName = 'MatrixRow';

export default function DPIAPage({ params }: DPIAPageProps) {
  const [lang, setLang] = useState<string>('');
  const [systemId, setSystemId] = useState<string>('');
  const [system, setSystem] = useState<System | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // DPIA Assessment State
  const [dpiaAssessments, setDpiaAssessments] = useState<DPIAAssessment[]>([]);

  // Matrix Data State
  const [matrixData, setMatrixData] = useState<SystemData[]>([]);
  const [isLoadingMatrix, setIsLoadingMatrix] = useState(false);

  // System Context State
  const [systemContext, setSystemContext] = useState<string>('');

  // View Mode State
  const [viewMode, setViewMode] = useState<'assessment' | 'dpia'>('assessment');

  // Pagination State
  const [currentPage, setCurrentPage] = useState(1);
  const [pageInput, setPageInput] = useState("");

  // Assessment State
  const [isExporting, setIsExporting] = useState(false);
  const [showAssessmentModal, setShowAssessmentModal] = useState(false);
  const [selectedAssessment, setSelectedAssessment] = useState<(DPIAAssessment & { domain?: string }) | null>(null);

  const router = useRouter();
  const { toast } = useToast();

  // Initialize params
  useEffect(() => {
    params.then(({ lang, systemId }) => {
      setLang(lang);
      setSystemId(systemId);
    });
  }, [params]);

  const loadSystemDetails = useCallback(async () => {
    if (!systemId) return;
    
    try {
      setIsLoading(true);
      const systems = await SystemsService.getSystems();
      const foundSystem = systems.find(s => s.id === systemId);

      if (foundSystem) {
        setSystem(foundSystem);
      } else {
        const isRTL = lang === "ar";
        toast({
          title: isRTL ? "النظام غير موجود" : "System not found",
          description: isRTL ? "لم يتم العثور على النظام المطلوب" : "The requested system was not found",
          variant: "destructive",
        });
        router.push(`/${lang}/Thiqah/DataClassification`);
      }
    } catch (error) {
      console.error('Error loading system:', error);
      const isRTL = lang === "ar";
      toast({
        title: isRTL ? "خطأ في تحميل النظام" : "Error loading system",
        description: isRTL ? "فشل في تحميل تفاصيل النظام" : "Failed to load system details",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }, [systemId, lang, toast, router]);

  const loadDPIAAssessments = useCallback(async () => {
    if (!systemId) return;

    try {
      const assessments = await SystemsService.getDPIAAssessments(systemId);
      setDpiaAssessments(assessments);
    } catch (error) {
      console.error('Error loading DPIA assessments:', error);
    }
  }, [systemId]);

  const loadSystemContext = useCallback(async () => {
    if (!systemId) return;

    try {
      const context = await SystemsService.getSystemContext(systemId);
      if (context) {
        setSystemContext(context.context || '');
      }
    } catch (error) {
      console.error('Error loading system context:', error);
    }
  }, [systemId]);

  const loadMatrixData = useCallback(async () => {
    if (!systemId) return;

    try {
      setIsLoadingMatrix(true);
      // Limit initial data load for better performance
      const allData = await SystemsService.getSystemData(systemId, 1000);

      // Filter for Secret/Top Secret AND Personal Data - optimized
      const filteredData = allData.filter(item =>
        item.hasPersonalData === true &&
        (item.confidentialityLevel === "Secret" || item.confidentialityLevel === "Top Secret")
      );

      setMatrixData(filteredData);
    } catch (error) {
      console.error('Error loading matrix data:', error);
      setMatrixData([]); // Set empty array on error
    } finally {
      setIsLoadingMatrix(false);
    }
  }, [systemId]);

  // Pagination calculations
  const rowsPerPage = viewMode === 'assessment' ? 5 : 50;
  const totalPages = Math.ceil(matrixData.length / rowsPerPage);
  const startIndex = (currentPage - 1) * rowsPerPage;
  const endIndex = startIndex + rowsPerPage;
  const currentData = matrixData.slice(startIndex, endIndex);

  // Memoize matrix stats for performance
  const matrixStats = useMemo(() => {
    const assessedItems = matrixData.filter(item =>
      dpiaAssessments.some(assessment => assessment.attributeId === item.id)
    );

    return {
      total: matrixData.length,
      topSecret: matrixData.filter(item => item.confidentialityLevel === "Top Secret").length,
      secret: matrixData.filter(item => item.confidentialityLevel === "Secret").length,
      assessed: assessedItems.length,
      pending: matrixData.length - assessedItems.length
    };
  }, [matrixData, dpiaAssessments]);

  // Handle view mode change
  const handleViewModeChange = (mode: 'assessment' | 'dpia') => {
    setViewMode(mode);
    setCurrentPage(1);
    setPageInput("");
  };

  // Handle page navigation
  const handlePageNavigation = (pageNumber: string) => {
    const page = parseInt(pageNumber);
    if (!isNaN(page) && page >= 1 && page <= totalPages) {
      setCurrentPage(page);
      setPageInput("");
      toast({
        title: isRTL ? "تم الانتقال للصفحة" : "Navigated to Page",
        description: isRTL ? `الصفحة ${page}` : `Page ${page}`,
      });
    } else {
      toast({
        title: isRTL ? "رقم صفحة غير صحيح" : "Invalid Page Number",
        description: isRTL ?
          `يرجى إدخال رقم بين 1 و ${totalPages}` :
          `Please enter a number between 1 and ${totalPages}`,
        variant: "destructive"
      });
    }
  };

  // Handle Enter key press in page input
  const handlePageInputKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      handlePageNavigation(pageInput);
    }
  };



  // Handle cell click to show assessment details
  const handleCellClick = (item: SystemData, domain: string) => {
    // Find assessment for this item
    const assessment = dpiaAssessments.find(a => a.attributeId === item.id);
    if (assessment) {
      setSelectedAssessment({ ...assessment, domain });
      setShowAssessmentModal(true);
    }
  };

  // Handle DPIA Export
  const handleDPIAExport = async () => {
    if (!systemId || dpiaAssessments.length === 0) {
      toast({
        title: isRTL ? "خطأ" : "Error",
        description: isRTL ? "لا توجد تقييمات DPIA للتصدير" : "No DPIA assessments available for export",
        variant: "destructive"
      });
      return;
    }

    try {
      setIsExporting(true);

      // Dynamic import for better performance
      const ExcelJS = await import('exceljs');

      // Create workbook
      const workbook = new ExcelJS.Workbook();

      // Thiqah brand colors (ARGB format for ExcelJS) - Light Blue Only
      const thiqahLightBlue = 'FF3B82F6';      // Light Thiqah blue #3B82F6 - PRIMARY COLOR
      const lightGray = 'FFF8FAFC';            // Very light gray background
      const white = 'FFFFFFFF';                // Pure white
      const darkText = 'FF1F2937';             // Dark text color

      // Risk level colors
      const riskColors = {
        HIGH: { bg: 'FFFEE2E2', font: 'FF991B1B' }, // Light red bg, dark red text
        MEDIUM: { bg: 'FFFEF3C7', font: 'FF92400E' }, // Light yellow bg, dark yellow text
        LOW: { bg: 'FFDCFCE7', font: 'FF166534' } // Light green bg, dark green text
      };

      // Risk level indicators
      const getRiskIndicator = (level: string) => {
        switch (level) {
          case 'HIGH': return '🔴 HIGH RISK';
          case 'MEDIUM': return '🟡 MEDIUM RISK';
          case 'LOW': return '🟢 LOW RISK';
          default: return level;
        }
      };

      // Clean reasoning text by removing risk level indicators
      const cleanReasoningText = (text: string) => {
        if (!text) return text;

        // Remove risk level indicators from the beginning of the text
        const cleanedText = text
          .replace(/^(HIGH|MEDIUM|LOW):\s*/i, '')  // Remove "HIGH: ", "MEDIUM: ", "LOW: "
          .replace(/^(High|Medium|Low),?\s*/i, '') // Remove "High, ", "Medium, ", "Low, "
          .replace(/^🔴\s*(HIGH|High)\s*[:-]?\s*/i, '') // Remove "🔴 HIGH: "
          .replace(/^🟡\s*(MEDIUM|Medium)\s*[:-]?\s*/i, '') // Remove "🟡 MEDIUM: "
          .replace(/^🟢\s*(LOW|Low)\s*[:-]?\s*/i, '') // Remove "🟢 LOW: "
          .trim();

        return cleanedText;
      };

      // Add Thiqah logo to workbook
      let logoImageId: number | undefined;

      // Try multiple methods to load the logo
      const logoLoadMethods = [
        () => fetch('/thiqah-logo.png'),
        () => fetch('./thiqah-logo.png'),
        () => fetch(`${window.location.origin}/thiqah-logo.png`)
      ];

      for (const loadMethod of logoLoadMethods) {
        try {
          console.log('Attempting to load Thiqah logo...');
          const logoResponse = await loadMethod();
          console.log('Logo response status:', logoResponse.status, logoResponse.statusText);

          if (logoResponse.ok) {
            const logoBuffer = await logoResponse.arrayBuffer();
            console.log('Logo buffer size:', logoBuffer.byteLength);

            if (logoBuffer.byteLength > 0) {
              logoImageId = workbook.addImage({
                buffer: logoBuffer,
                extension: 'png',
              });
              console.log('Logo successfully added to workbook with ID:', logoImageId);
              break; // Success, exit the loop
            }
          } else {
            console.warn('Failed to fetch logo:', logoResponse.status, logoResponse.statusText);
          }
        } catch (error) {
          console.warn('Logo loading method failed:', error);
          continue; // Try next method
        }
      }

      if (!logoImageId) {
        console.error('All logo loading methods failed');
      }

      // Create Executive Summary Sheet (First Sheet)
      const executiveSummary = workbook.addWorksheet('Executive Summary');

      // Set column widths for executive summary
      executiveSummary.columns = [
        { width: 3 },   // A - Spacer
        { width: 25 },  // B - Labels
        { width: 35 },  // C - Values
        { width: 3 },   // D - Spacer
        { width: 25 },  // E - Labels
        { width: 35 },  // F - Values
        { width: 3 },   // G - Spacer
      ];

      // Add Thiqah logo if available
      if (logoImageId) {
        console.log('Adding logo to executive summary...');
        try {
          executiveSummary.addImage(logoImageId, {
            tl: { col: 0.5, row: 0.5 }, // Top-left position (A1 area)
            ext: { width: 150, height: 75 }, // Larger size for better visibility
            editAs: 'oneCell'
          });
          console.log('Logo successfully added to executive summary');
        } catch (logoError) {
          console.error('Error adding logo to sheet:', logoError);
        }
      }

      // Always add text-based Thiqah logo for guaranteed visibility
      executiveSummary.mergeCells('A1:C2');
      const textLogoCell = executiveSummary.getCell('A1');
      textLogoCell.value = 'THIQAH\nثقة';
      textLogoCell.font = { size: 18, bold: true, color: { argb: white } };
      textLogoCell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
      textLogoCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: thiqahLightBlue } };
      textLogoCell.border = {
        top: { style: 'thick', color: { argb: thiqahLightBlue } },
        bottom: { style: 'thick', color: { argb: thiqahLightBlue } },
        left: { style: 'thick', color: { argb: thiqahLightBlue } },
        right: { style: 'thick', color: { argb: thiqahLightBlue } }
      };

      // Add header with Thiqah branding
      executiveSummary.mergeCells('D1:F2');
      const brandingCell = executiveSummary.getCell('D1');
      brandingCell.value = 'THIQAH\nSaudi Digital Government Authority';
      brandingCell.font = { size: 12, bold: true, color: { argb: thiqahLightBlue } };
      brandingCell.alignment = { horizontal: 'right', vertical: 'middle', wrapText: true };
      executiveSummary.getRow(1).height = 40;
      executiveSummary.getRow(2).height = 40;

      // Add main title
      executiveSummary.mergeCells('B4:F4');
      const execTitleCell = executiveSummary.getCell('B4');
      execTitleCell.value = 'Data Protection Impact Assessment (DPIA) Report';
      execTitleCell.font = { size: 20, bold: true, color: { argb: white } };
      execTitleCell.alignment = { horizontal: 'center', vertical: 'middle' };
      execTitleCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: thiqahLightBlue } };
      execTitleCell.border = {
        top: { style: 'thick', color: { argb: thiqahLightBlue } },
        bottom: { style: 'thick', color: { argb: thiqahLightBlue } },
        left: { style: 'thick', color: { argb: thiqahLightBlue } },
        right: { style: 'thick', color: { argb: thiqahLightBlue } }
      };
      executiveSummary.getRow(4).height = 40;

      // Add subtitle
      executiveSummary.mergeCells('B5:F5');
      const subtitleCell = executiveSummary.getCell('B5');
      subtitleCell.value = 'Comprehensive Risk Assessment for Personal Data Processing';
      subtitleCell.font = { size: 12, italic: true, color: { argb: thiqahLightBlue } };
      subtitleCell.alignment = { horizontal: 'center', vertical: 'middle' };
      subtitleCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: lightGray } };
      executiveSummary.getRow(5).height = 25;

      // System Information Section
      let currentRow = 7;

      // Section header
      executiveSummary.mergeCells(`B${currentRow}:F${currentRow}`);
      const systemHeaderCell = executiveSummary.getCell(`B${currentRow}`);
      systemHeaderCell.value = '📊 SYSTEM INFORMATION';
      systemHeaderCell.font = { size: 14, bold: true, color: { argb: white } };
      systemHeaderCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: thiqahLightBlue } };
      systemHeaderCell.alignment = { horizontal: 'center', vertical: 'middle' };
      systemHeaderCell.border = {
        top: { style: 'thin' }, bottom: { style: 'thin' },
        left: { style: 'thin' }, right: { style: 'thin' }
      };
      executiveSummary.getRow(currentRow).height = 30;
      currentRow++;

      // System details
      const systemDetails = [
        ['System Name', system?.name || 'N/A'],
        ['Responsible Owner', system?.responsibleOwner || 'N/A'],
        ['Database Administrator', system?.dba || 'N/A'],
        ['Contact Email', system?.email || 'N/A'],
        ['Assessment Date', new Date().toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        })],
        ['Total Attributes Assessed', dpiaAssessments.length.toString()],
        ['System Context Available', systemContext ? 'Yes' : 'No']
      ];

      systemDetails.forEach(([label, value]) => {
        // Label
        const labelCell = executiveSummary.getCell(`B${currentRow}`);
        labelCell.value = label;
        labelCell.font = { bold: true, color: { argb: thiqahLightBlue } };
        labelCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: lightGray } };
        labelCell.border = {
          top: { style: 'thin' }, bottom: { style: 'thin' },
          left: { style: 'thin' }, right: { style: 'thin' }
        };

        // Value
        const valueCell = executiveSummary.getCell(`C${currentRow}`);
        valueCell.value = value;
        valueCell.font = { color: { argb: darkText } };
        valueCell.border = {
          top: { style: 'thin' }, bottom: { style: 'thin' },
          left: { style: 'thin' }, right: { style: 'thin' }
        };

        executiveSummary.getRow(currentRow).height = 25;
        currentRow++;
      });

      // Risk Analysis Section
      currentRow += 2; // Add some spacing

      // Risk analysis header
      executiveSummary.mergeCells(`B${currentRow}:F${currentRow}`);
      const riskHeaderCell = executiveSummary.getCell(`B${currentRow}`);
      riskHeaderCell.value = '⚠️ RISK ANALYSIS OVERVIEW';
      riskHeaderCell.font = { size: 14, bold: true, color: { argb: white } };
      riskHeaderCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: thiqahLightBlue } };
      riskHeaderCell.alignment = { horizontal: 'center', vertical: 'middle' };
      riskHeaderCell.border = {
        top: { style: 'thin' }, bottom: { style: 'thin' },
        left: { style: 'thin' }, right: { style: 'thin' }
      };
      executiveSummary.getRow(currentRow).height = 30;
      currentRow++;

      // Calculate risk statistics
      const riskStats = {
        HIGH: dpiaAssessments.filter(a => a.finalRiskLevel === 'HIGH').length,
        MEDIUM: dpiaAssessments.filter(a => a.finalRiskLevel === 'MEDIUM').length,
        LOW: dpiaAssessments.filter(a => a.finalRiskLevel === 'LOW').length
      };

      const totalAssessments = dpiaAssessments.length;
      const highestRisk = riskStats.HIGH > 0 ? 'HIGH' : riskStats.MEDIUM > 0 ? 'MEDIUM' : 'LOW';

      // Risk summary data
      const riskSummaryData = [
        ['Overall Risk Level', `${getRiskIndicator(highestRisk)}`],
        ['High Risk Attributes', `${riskStats.HIGH} (${totalAssessments > 0 ? Math.round((riskStats.HIGH / totalAssessments) * 100) : 0}%)`],
        ['Medium Risk Attributes', `${riskStats.MEDIUM} (${totalAssessments > 0 ? Math.round((riskStats.MEDIUM / totalAssessments) * 100) : 0}%)`],
        ['Low Risk Attributes', `${riskStats.LOW} (${totalAssessments > 0 ? Math.round((riskStats.LOW / totalAssessments) * 100) : 0}%)`],
        ['Total Attributes', totalAssessments.toString()],
        ['Compliance Status', riskStats.HIGH === 0 ? '✅ Compliant' : '⚠️ Requires Attention']
      ];

      riskSummaryData.forEach(([label, value]) => {
        // Label
        const labelCell = executiveSummary.getCell(`B${currentRow}`);
        labelCell.value = label;
        labelCell.font = { bold: true, color: { argb: thiqahLightBlue } };
        labelCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: lightGray } };
        labelCell.border = {
          top: { style: 'thin' }, bottom: { style: 'thin' },
          left: { style: 'thin' }, right: { style: 'thin' }
        };

        // Value with conditional formatting
        const valueCell = executiveSummary.getCell(`C${currentRow}`);
        valueCell.value = value;

        // Apply risk-based coloring
        if (label === 'Overall Risk Level') {
          if (value.includes('HIGH')) {
            valueCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: riskColors.HIGH.bg } };
            valueCell.font = { bold: true, color: { argb: riskColors.HIGH.font } };
          } else if (value.includes('MEDIUM')) {
            valueCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: riskColors.MEDIUM.bg } };
            valueCell.font = { bold: true, color: { argb: riskColors.MEDIUM.font } };
          } else {
            valueCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: riskColors.LOW.bg } };
            valueCell.font = { bold: true, color: { argb: riskColors.LOW.font } };
          }
        } else {
          valueCell.font = { color: { argb: darkText } };
        }

        valueCell.border = {
          top: { style: 'thin' }, bottom: { style: 'thin' },
          left: { style: 'thin' }, right: { style: 'thin' }
        };

        executiveSummary.getRow(currentRow).height = 25;
        currentRow++;
      });

      // Key Findings Section
      currentRow += 2;

      executiveSummary.mergeCells(`B${currentRow}:F${currentRow}`);
      const findingsHeaderCell = executiveSummary.getCell(`B${currentRow}`);
      findingsHeaderCell.value = '🔍 KEY FINDINGS & RECOMMENDATIONS';
      findingsHeaderCell.font = { size: 14, bold: true, color: { argb: white } };
      findingsHeaderCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: thiqahLightBlue } };
      findingsHeaderCell.alignment = { horizontal: 'center', vertical: 'middle' };
      findingsHeaderCell.border = {
        top: { style: 'thin' }, bottom: { style: 'thin' },
        left: { style: 'thin' }, right: { style: 'thin' }
      };
      executiveSummary.getRow(currentRow).height = 30;
      currentRow++;

      // Generate key findings based on risk analysis
      const keyFindings = [];

      if (riskStats.HIGH > 0) {
        keyFindings.push(`⚠️ CRITICAL: ${riskStats.HIGH} attributes identified with HIGH risk requiring immediate attention`);
        keyFindings.push('📋 Recommendation: Implement additional security controls and data minimization strategies');
      }

      if (riskStats.MEDIUM > 0) {
        keyFindings.push(`🔶 MODERATE: ${riskStats.MEDIUM} attributes with MEDIUM risk requiring monitoring and controls`);
        keyFindings.push('📋 Recommendation: Establish regular review processes and enhanced access controls');
      }

      if (riskStats.LOW === totalAssessments && totalAssessments > 0) {
        keyFindings.push('✅ POSITIVE: All assessed attributes show LOW risk levels');
        keyFindings.push('📋 Recommendation: Maintain current security posture with periodic reviews');
      }

      keyFindings.push(`📊 Assessment Coverage: ${totalAssessments} personal data attributes evaluated`);
      keyFindings.push('🔒 Next Steps: Review detailed domain-specific assessments in subsequent sheets');

      keyFindings.forEach((finding) => {
        executiveSummary.mergeCells(`B${currentRow}:F${currentRow}`);
        const findingCell = executiveSummary.getCell(`B${currentRow}`);
        findingCell.value = finding;
        findingCell.font = { size: 11, color: { argb: darkText } };
        findingCell.alignment = { horizontal: 'left', vertical: 'middle', wrapText: true };
        findingCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: lightGray } };
        findingCell.border = {
          top: { style: 'thin' }, bottom: { style: 'thin' },
          left: { style: 'thin' }, right: { style: 'thin' }
        };
        executiveSummary.getRow(currentRow).height = 30;
        currentRow++;
      });

      // Add footer with Thiqah branding
      currentRow += 3;
      executiveSummary.mergeCells(`B${currentRow}:F${currentRow}`);
      const footerCell = executiveSummary.getCell(`B${currentRow}`);
      footerCell.value = '© 2024 Thiqah - Saudi Digital Government Authority | Data Protection Impact Assessment';
      footerCell.font = { size: 10, italic: true, color: { argb: thiqahLightBlue } };
      footerCell.alignment = { horizontal: 'center', vertical: 'middle' };
      footerCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: lightGray } };
      footerCell.border = {
        top: { style: 'thin' }, bottom: { style: 'thin' },
        left: { style: 'thin' }, right: { style: 'thin' }
      };
      executiveSummary.getRow(currentRow).height = 25;

      // Create Summary Sheet (Second Sheet)
      const summarySheet = workbook.addWorksheet('Risk Summary');

      // Add logo to summary sheet if available
      if (logoImageId) {
        try {
          summarySheet.addImage(logoImageId, {
            tl: { col: 3, row: 0 }, // Top-right position
            ext: { width: 100, height: 50 },
            editAs: 'oneCell'
          });
        } catch (logoError) {
          console.warn('Could not add logo to summary sheet:', logoError);
        }
      }

      // Set column widths
      summarySheet.columns = [
        { width: 25 },
        { width: 35 },
        { width: 15 },
        { width: 15 }
      ];

      // Add title
      summarySheet.mergeCells('A1:D1');
      const titleCell = summarySheet.getCell('A1');
      titleCell.value = 'DPIA ASSESSMENT REPORT - THIQAH';
      titleCell.font = { bold: true, color: { argb: white }, size: 16 };
      titleCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: thiqahLightBlue } };
      titleCell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
      titleCell.border = {
        top: { style: 'thin' }, bottom: { style: 'thin' },
        left: { style: 'thin' }, right: { style: 'thin' }
      };
      summarySheet.getRow(1).height = 30;

      // Add system information
      summarySheet.getCell('A2').value = 'System Name';
      summarySheet.getCell('B2').value = system?.name || 'Unknown System';
      summarySheet.getCell('A3').value = 'Export Date';
      summarySheet.getCell('B3').value = new Date().toLocaleDateString();
      summarySheet.getCell('A4').value = 'Total Assessments';
      summarySheet.getCell('B4').value = dpiaAssessments.length;

      // Add risk level summary header
      summarySheet.mergeCells('A6:D6');
      const sectionCell = summarySheet.getCell('A6');
      sectionCell.value = 'RISK LEVEL SUMMARY';
      sectionCell.font = { bold: true, color: { argb: white }, size: 14 };
      sectionCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: thiqahLightBlue } };
      sectionCell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
      sectionCell.border = {
        top: { style: 'thin' }, bottom: { style: 'thin' },
        left: { style: 'thin' }, right: { style: 'thin' }
      };
      summarySheet.getRow(6).height = 25;

      // Add risk level data
      const riskData = [
        { label: '🔴 High Risk', count: dpiaAssessments.filter(a => a.finalRiskLevel === 'HIGH').length },
        { label: '🟡 Medium Risk', count: dpiaAssessments.filter(a => a.finalRiskLevel === 'MEDIUM').length },
        { label: '🟢 Low Risk', count: dpiaAssessments.filter(a => a.finalRiskLevel === 'LOW').length }
      ];

      riskData.forEach((risk, index) => {
        const row = 7 + index;
        const labelCell = summarySheet.getCell(`A${row}`);
        const countCell = summarySheet.getCell(`B${row}`);

        labelCell.value = risk.label;
        labelCell.font = { bold: true, size: 11 };
        labelCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: lightGray } };
        labelCell.alignment = { vertical: 'middle', wrapText: true };

        countCell.value = risk.count;
        countCell.font = { bold: true, size: 11 };
        countCell.alignment = { horizontal: 'center', vertical: 'middle' };

        summarySheet.getRow(row).height = 20;
      });

      // Create Detailed Assessments Sheet
      const detailSheet = workbook.addWorksheet('Detailed Assessments');

      // Define headers (clean, professional - no icons)
      const detailHeaders = [
        'Table Name',
        'Column Name',
        'Overall Risk',
        'Individual Harm - Identity Theft',
        'Individual Harm - Financial Exploitation',
        'Individual Harm - Privacy Invasion',
        'Individual Harm - Psychological Impact',
        'Business Reputation - Trust Erosion',
        'Business Reputation - Media Coverage',
        'Business Reputation - Brand Damage',
        'Business Reputation - Competitive Issues',
        'Financial - Regulatory Fines',
        'Financial - Legal Costs',
        'Legal/Regulatory - PDPL Violations',
        'Legal/Regulatory - Criminal Liability',
        'Legal/Regulatory - International Issues',
        'Overall Impact'
      ];

      // Set column widths
      detailSheet.columns = detailHeaders.map(() => ({ width: 30 }));

      // Add headers
      detailSheet.addRow(detailHeaders);
      const headerRow = detailSheet.getRow(1);
      headerRow.height = 40;

      // Style headers
      headerRow.eachCell((cell) => {
        cell.font = { bold: true, color: { argb: white }, size: 12 };
        cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: thiqahLightBlue } };
        cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
        cell.border = {
          top: { style: 'thin' }, bottom: { style: 'thin' },
          left: { style: 'thin' }, right: { style: 'thin' }
        };
      });

      // Add data rows
      dpiaAssessments.forEach((assessment) => {
        const rowData = [
          assessment.tableName,
          assessment.columnName,
          getRiskIndicator(assessment.finalRiskLevel),
          cleanReasoningText(assessment.domainAnswers.individualHarm.identityRecreation),
          cleanReasoningText(assessment.domainAnswers.individualHarm.financialExploitation),
          cleanReasoningText(assessment.domainAnswers.individualHarm.privacyInvasion),
          cleanReasoningText(assessment.domainAnswers.individualHarm.psychologicalImpact),
          cleanReasoningText(assessment.domainAnswers.businessReputation.trustErosion),
          cleanReasoningText(assessment.domainAnswers.businessReputation.mediaCoverage),
          cleanReasoningText(assessment.domainAnswers.businessReputation.brandDamage),
          cleanReasoningText(assessment.domainAnswers.businessReputation.competitiveDisadvantage),
          cleanReasoningText(assessment.domainAnswers.financialImpact.regulatoryFines),
          cleanReasoningText(assessment.domainAnswers.financialImpact.legalCosts),
          cleanReasoningText(assessment.domainAnswers.legalRegulatory.pdplViolations),
          cleanReasoningText(assessment.domainAnswers.legalRegulatory.criminalLiability),
          cleanReasoningText(assessment.domainAnswers.legalRegulatory.internationalIssues),
          cleanReasoningText(assessment.overallImpact)
        ];

        const row = detailSheet.addRow(rowData);
        row.height = 60;

        // Style data cells
        row.eachCell((cell, colNumber) => {
          cell.alignment = { vertical: 'top', wrapText: true };
          cell.border = {
            top: { style: 'thin', color: { argb: 'FFE5E7EB' } },
            bottom: { style: 'thin', color: { argb: 'FFE5E7EB' } },
            left: { style: 'thin', color: { argb: 'FFE5E7EB' } },
            right: { style: 'thin', color: { argb: 'FFE5E7EB' } }
          };

          // Special styling for risk level column (column 3)
          if (colNumber === 3) {
            const riskLevel = assessment.finalRiskLevel as keyof typeof riskColors;
            if (riskColors[riskLevel]) {
              cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: riskColors[riskLevel].bg } };
              cell.font = { color: { argb: riskColors[riskLevel].font }, bold: true };
              cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
            }
          }
        });
      });

      // Create domain-specific sheets (clean, professional)
      const domains = [
        {
          name: 'Individual Harm',
          headers: ['Table', 'Column', 'Risk Level', 'Identity Theft', 'Financial Exploitation', 'Privacy Invasion', 'Psychological Impact'],
          getData: (a: DPIAAssessment) => [
            a.tableName,
            a.columnName,
            getRiskIndicator(a.domainAnswers.individualHarm.riskLevel || a.finalRiskLevel),
            cleanReasoningText(a.domainAnswers.individualHarm.identityRecreation),
            cleanReasoningText(a.domainAnswers.individualHarm.financialExploitation),
            cleanReasoningText(a.domainAnswers.individualHarm.privacyInvasion),
            cleanReasoningText(a.domainAnswers.individualHarm.psychologicalImpact)
          ]
        },
        {
          name: 'Business Reputation',
          headers: ['Table', 'Column', 'Risk Level', 'Trust Erosion', 'Media Coverage', 'Brand Damage', 'Competitive Issues'],
          getData: (a: DPIAAssessment) => [
            a.tableName,
            a.columnName,
            getRiskIndicator(a.domainAnswers.businessReputation.riskLevel || a.finalRiskLevel),
            cleanReasoningText(a.domainAnswers.businessReputation.trustErosion),
            cleanReasoningText(a.domainAnswers.businessReputation.mediaCoverage),
            cleanReasoningText(a.domainAnswers.businessReputation.brandDamage),
            cleanReasoningText(a.domainAnswers.businessReputation.competitiveDisadvantage)
          ]
        },
        {
          name: 'Financial Impact',
          headers: ['Table', 'Column', 'Risk Level', 'Regulatory Fines', 'Legal Costs'],
          getData: (a: DPIAAssessment) => [
            a.tableName,
            a.columnName,
            getRiskIndicator(a.domainAnswers.financialImpact.riskLevel || a.finalRiskLevel),
            cleanReasoningText(a.domainAnswers.financialImpact.regulatoryFines),
            cleanReasoningText(a.domainAnswers.financialImpact.legalCosts)
          ]
        },
        {
          name: 'Legal Regulatory',
          headers: ['Table', 'Column', 'Risk Level', 'PDPL Violations', 'Criminal Liability', 'International Issues'],
          getData: (a: DPIAAssessment) => [
            a.tableName,
            a.columnName,
            getRiskIndicator(a.domainAnswers.legalRegulatory.riskLevel || a.finalRiskLevel),
            cleanReasoningText(a.domainAnswers.legalRegulatory.pdplViolations),
            cleanReasoningText(a.domainAnswers.legalRegulatory.criminalLiability),
            cleanReasoningText(a.domainAnswers.legalRegulatory.internationalIssues)
          ]
        }
      ];

      // Create domain sheets
      domains.forEach(domain => {
        const domainSheet = workbook.addWorksheet(domain.name);

        // Set column widths
        domainSheet.columns = domain.headers.map(() => ({ width: 30 }));

        // Add headers
        domainSheet.addRow(domain.headers);
        const headerRow = domainSheet.getRow(1);
        headerRow.height = 40;

        // Style headers
        headerRow.eachCell((cell) => {
          cell.font = { bold: true, color: { argb: white }, size: 12 };
          cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: thiqahLightBlue } };
          cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
          cell.border = {
            top: { style: 'thin' }, bottom: { style: 'thin' },
            left: { style: 'thin' }, right: { style: 'thin' }
          };
        });

        // Add data rows
        dpiaAssessments.forEach((assessment) => {
          const rowData = domain.getData(assessment);
          const row = domainSheet.addRow(rowData);
          row.height = 60;

          // Style data cells
          row.eachCell((cell, colNumber) => {
            cell.alignment = { vertical: 'top', wrapText: true };
            cell.border = {
              top: { style: 'thin', color: { argb: 'FFE5E7EB' } },
              bottom: { style: 'thin', color: { argb: 'FFE5E7EB' } },
              left: { style: 'thin', color: { argb: 'FFE5E7EB' } },
              right: { style: 'thin', color: { argb: 'FFE5E7EB' } }
            };

            // Special styling for risk level column (column 3)
            if (colNumber === 3) {
              const riskLevel = assessment.finalRiskLevel as keyof typeof riskColors;
              if (riskColors[riskLevel]) {
                cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: riskColors[riskLevel].bg } };
                cell.font = { color: { argb: riskColors[riskLevel].font }, bold: true };
                cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
              }
            }
          });
        });
      });

      // Generate filename and export
      const timestamp = new Date().toISOString().split('T')[0];
      const filename = `DPIA_Assessment_${system?.name || 'System'}_${timestamp}.xlsx`;

      // Export file using ExcelJS
      const buffer = await workbook.xlsx.writeBuffer();
      const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = filename;
      link.click();
      window.URL.revokeObjectURL(url);

      toast({
        title: isRTL ? "تم التصدير بنجاح" : "Export Successful",
        description: isRTL ?
          `تم تصدير ${dpiaAssessments.length} تقييم DPIA` :
          `Exported ${dpiaAssessments.length} DPIA assessments`,
      });

    } catch (error) {
      console.error('Export error:', error);
      toast({
        title: isRTL ? "خطأ في التصدير" : "Export Error",
        description: isRTL ?
          "فشل في تصدير تقييمات DPIA. يرجى المحاولة مرة أخرى." :
          "Failed to export DPIA assessments. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsExporting(false);
    }
  };

  useEffect(() => {
    if (systemId) {
      loadSystemDetails();
      loadDPIAAssessments();
      loadMatrixData();
      loadSystemContext();
    }
  }, [systemId, loadSystemDetails, loadDPIAAssessments, loadMatrixData, loadSystemContext]);

  // Reset pagination when view mode changes
  useEffect(() => {
    setCurrentPage(1);
    setPageInput("");
  }, [viewMode]);



  const handleGoBack = () => {
    router.push(`/${lang}/Thiqah/DataClassification/${systemId}`);
  };

  if (!lang || !systemId) {
    return <div>Loading...</div>;
  }

  const isRTL = lang === "ar";

  if (isLoading) {
    return (
      <div className={`min-h-screen bg-gray-50 ${isRTL ? "rtl" : "ltr"}`}>
        <div className="relative min-h-screen bg-gradient-to-br from-[var(--brand-blue)] via-[var(--brand-blue)]/95 to-[var(--brand-dark-gray)] overflow-hidden">
          <div className="relative z-10 flex flex-col justify-center items-center min-h-screen px-8 py-16">
            <div className="animate-pulse text-center">
              <div className="w-24 h-24 bg-white/20 rounded-3xl mx-auto mb-8"></div>
              <div className="h-12 bg-white/20 rounded-lg w-96 mx-auto mb-4"></div>
              <div className="h-6 bg-white/20 rounded-lg w-64 mx-auto"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`min-h-screen bg-gray-50 ${isRTL ? "rtl" : "ltr"}`}>
      {/* Header */}
      <div className="relative bg-gradient-to-r from-[var(--brand-blue)] to-[var(--brand-blue)]/80">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-0 left-0 w-32 h-32 bg-white rounded-full -translate-x-1/2 -translate-y-1/2"></div>
          <div className="absolute bottom-0 right-0 w-32 h-32 bg-white rounded-full translate-x-1/2 translate-y-1/2"></div>
        </div>

        <div className="relative z-10 px-8 py-8">
          <div className="flex items-center justify-between mb-6">
            {/* Back Button */}
            <Button
              onClick={handleGoBack}
              className="bg-white/10 backdrop-blur-xl border border-white/20 text-white hover:bg-white/20 hover:text-white shadow-lg"
            >
              {isRTL ? <ArrowRight className="w-4 h-4 mr-2" /> : <ArrowLeft className="w-4 h-4 mr-2" />}
              {isRTL ? "العودة" : "Back"}
            </Button>
          </div>

          {/* Page Header */}
          <div className="flex items-center gap-6 mb-6">
            <div className="w-16 h-16 bg-white/20 backdrop-blur-xl rounded-2xl flex items-center justify-center shadow-lg border border-white/20">
              <FileCheck className="w-8 h-8 text-white" />
            </div>
            <div>
              <h1 className="text-3xl md:text-4xl font-bold text-white mb-1 tracking-tight">
                {isRTL ? "تقييم أثر حماية البيانات" : "DPIA - Data Protection Impact Assessment"}
              </h1>
              <p className="text-white/90 text-lg">
                {system?.name}
              </p>
            </div>
          </div>

          {/* Summary Stats */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="bg-white/10 backdrop-blur-xl rounded-xl p-4 border border-white/20">
              <div className="flex items-center gap-3">
                <div className="p-2 rounded-lg bg-white/20 text-white">
                  <CheckCircle className="w-4 h-4" />
                </div>
                <div className="min-w-0 flex-1">
                  <div className="text-xs font-semibold text-white/80 uppercase tracking-wider">
                    {isRTL ? "تم التقييم" : "Assessed"}
                  </div>
                  <div className="text-sm font-bold text-white">{dpiaAssessments.length}</div>
                </div>
              </div>
            </div>
            <div className="bg-white/10 backdrop-blur-xl rounded-xl p-4 border border-white/20">
              <div className="flex items-center gap-3">
                <div className="p-2 rounded-lg bg-white/20 text-white">
                  <FileCheck className="w-4 h-4" />
                </div>
                <div className="min-w-0 flex-1">
                  <div className="text-xs font-semibold text-white/80 uppercase tracking-wider">
                    {isRTL ? "حالة التقييم" : "Assessment Status"}
                  </div>
                  <div className="text-sm font-bold text-white">
                    {dpiaAssessments.length > 0 ? (isRTL ? "نشط" : "Active") : (isRTL ? "لم يبدأ" : "Not Started")}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* DPIA Risk Assessment Matrix */}
      <div className="px-6 py-6">
        <div className="max-w-7xl mx-auto">
          <div className="bg-white rounded-2xl shadow-lg border border-gray-200 overflow-hidden">
            {/* Matrix Header */}
            <div className="p-6 border-b border-gray-200 bg-gray-50">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                    <Table className="w-5 h-5 text-blue-600" />
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-gray-900">
                      {isRTL ? "مصفوفة تقييم مخاطر DPIA" : "DPIA Risk Assessment Matrix"}
                    </h3>
                    <p className="text-gray-600 text-sm">
                      {isRTL
                        ? "تقييم المخاطر للبيانات الشخصية المصنفة كسرية أو سرية للغاية"
                        : "Risk assessment for personal data classified as Secret or Top Secret"
                      }
                    </p>
                  </div>
                </div>

                {/* View Mode Toggle */}
                <div className="bg-white rounded-xl p-1 border border-gray-200 shadow-sm">
                  <div className="flex gap-1">
                    <button
                      onClick={() => handleViewModeChange('assessment')}
                      className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-all duration-300 ${
                        viewMode === 'assessment'
                          ? 'bg-[var(--brand-blue)] text-white shadow-md'
                          : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                      }`}
                    >
                      <Eye className="w-4 h-4" />
                      <span className="font-medium">
                        {isRTL ? "عرض التقييم" : "Assessment View"}
                      </span>
                      <span className="text-xs opacity-75">
                        ({isRTL ? "5 صفوف" : "5 rows"})
                      </span>
                    </button>
                    <button
                      onClick={() => handleViewModeChange('dpia')}
                      className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-all duration-300 ${
                        viewMode === 'dpia'
                          ? 'bg-[var(--brand-blue)] text-white shadow-md'
                          : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                      }`}
                    >
                      <BarChart3 className="w-4 h-4" />
                      <span className="font-medium">
                        {isRTL ? "عرض DPIA" : "DPIA View"}
                      </span>
                      <span className="text-xs opacity-75">
                        ({isRTL ? "50 صف" : "50 rows"})
                      </span>
                    </button>
                  </div>
                </div>
              </div>

              {/* Stats, Risk Legend, and Assessment Button */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-6">
                  {/* Data Stats */}
                  <div className="flex items-center gap-4 text-xs text-gray-600">
                    <div className="flex items-center gap-1">
                      <Database className="w-3 h-3" />
                      <span>{isRTL ? "إجمالي:" : "Total:"} {matrixStats.total}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                      <span>{matrixStats.topSecret}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                      <span>{matrixStats.secret}</span>
                    </div>
                    <div className="flex items-center gap-1 ml-2">
                      <div className="w-2 h-2 bg-[var(--brand-blue)] rounded-full"></div>
                      <span>{isRTL ? "مُقيم:" : "Assessed:"} {matrixStats.assessed}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <div className="w-2 h-2 bg-gray-400 rounded-full"></div>
                      <span>{isRTL ? "معلق:" : "Pending:"} {matrixStats.pending}</span>
                    </div>
                    {totalPages > 1 && (
                      <div className="flex items-center gap-1 ml-4">
                        <span>{isRTL ? "الصفحة:" : "Page:"} {currentPage} {isRTL ? "من" : "of"} {totalPages}</span>
                      </div>
                    )}
                  </div>

                  {/* Risk Level Legend */}
                  <div className="flex items-center gap-3 text-xs">
                    <span className="text-gray-500 font-medium">
                      {isRTL ? "مستويات المخاطر:" : "Risk Levels:"}
                    </span>
                    <div className="flex items-center gap-1">
                      <div className="w-3 h-3 bg-red-500 rounded-sm"></div>
                      <span className="text-gray-600">{isRTL ? "عالي" : "High"}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <div className="w-3 h-3 bg-yellow-500 rounded-sm"></div>
                      <span className="text-gray-600">{isRTL ? "متوسط" : "Medium"}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <div className="w-3 h-3 bg-green-500 rounded-sm"></div>
                      <span className="text-gray-600">{isRTL ? "منخفض" : "Low"}</span>
                    </div>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex items-center gap-3">
                  {/* Export Button - Show when there are assessments */}
                  {dpiaAssessments.length > 0 && (
                    <Button
                      onClick={handleDPIAExport}
                      disabled={isExporting}
                      variant="outline"
                      className="border-[var(--brand-blue)] text-[var(--brand-blue)] hover:bg-[var(--brand-blue)] hover:text-white"
                    >
                      {isExporting ? (
                        <>
                          <div className="w-4 h-4 mr-2 animate-spin rounded-full border-2 border-current border-t-transparent" />
                          {isRTL ? "جاري التصدير..." : "Exporting..."}
                        </>
                      ) : (
                        <>
                          <Download className="w-4 h-4 mr-2" />
                          {isRTL ? "تصدير DPIA" : "Export DPIA"}
                          <span className="ml-1 text-xs opacity-75">
                            ({dpiaAssessments.length})
                          </span>
                        </>
                      )}
                    </Button>
                  )}


                </div>
              </div>
            </div>

            {/* Matrix Content */}
            <div className="overflow-x-auto">
              {isLoadingMatrix ? (
                <div className="flex items-center justify-center py-8">
                  <div className="animate-pulse">
                    <div className="w-8 h-8 bg-gray-200 rounded-lg"></div>
                  </div>
                </div>
              ) : matrixData.length === 0 ? (
                <div className="text-center py-8">
                  <div className="w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                    <Table className="w-8 h-8 text-gray-400" />
                  </div>
                  <p className="text-gray-600 text-sm">
                    {isRTL
                      ? "لا توجد بيانات شخصية مصنفة كسرية أو سرية للغاية"
                      : "No personal data classified as Secret or Top Secret found"
                    }
                  </p>
                </div>
              ) : (
                <div className="min-w-[800px]">
                  {/* Matrix Table */}
                  <table className="w-full">
                    <thead>
                      <tr className="bg-gray-50 border-b border-gray-200">
                        <th className="p-4 text-left font-semibold text-gray-900 border-r border-gray-200 min-w-[250px]">
                          <div className="flex items-center gap-2">
                            <Database className="w-4 h-4 text-gray-600" />
                            {isRTL ? "الجدول / العمود" : "Table / Column"}
                          </div>
                        </th>
                        <th className="p-4 text-center font-semibold text-gray-900 border-r border-gray-200 min-w-[150px] bg-gray-50">
                          <div className="flex flex-col items-center gap-2">
                            <div className="w-8 h-8 bg-[var(--brand-blue)] rounded-lg flex items-center justify-center">
                              <User className="w-4 h-4 text-white" />
                            </div>
                            <div className="text-center">
                              <div className="text-sm font-semibold text-gray-900">{isRTL ? "الضرر الفردي" : "Individual Harm"}</div>
                              <div className="text-xs text-gray-600">{isRTL ? "التأثير على الأفراد" : "Impact on Individuals"}</div>
                            </div>
                          </div>
                        </th>
                        <th className="p-4 text-center font-semibold text-gray-900 border-r border-gray-200 min-w-[150px] bg-gray-50">
                          <div className="flex flex-col items-center gap-2">
                            <div className="w-8 h-8 bg-[var(--brand-blue)] rounded-lg flex items-center justify-center">
                              <Building className="w-4 h-4 text-white" />
                            </div>
                            <div className="text-center">
                              <div className="text-sm font-semibold text-gray-900">{isRTL ? "سمعة الأعمال" : "Business Reputation"}</div>
                              <div className="text-xs text-gray-600">{isRTL ? "تأثير السمعة" : "Reputation Impact"}</div>
                            </div>
                          </div>
                        </th>
                        <th className="p-4 text-center font-semibold text-gray-900 border-r border-gray-200 min-w-[150px] bg-gray-50">
                          <div className="flex flex-col items-center gap-2">
                            <div className="w-8 h-8 bg-[var(--brand-blue)] rounded-lg flex items-center justify-center">
                              <DollarSign className="w-4 h-4 text-white" />
                            </div>
                            <div className="text-center">
                              <div className="text-sm font-semibold text-gray-900">{isRTL ? "العواقب المالية" : "Financial Consequences"}</div>
                              <div className="text-xs text-gray-600">{isRTL ? "التكاليف والغرامات" : "Costs & Fines"}</div>
                            </div>
                          </div>
                        </th>
                        <th className="p-4 text-center font-semibold text-gray-900 min-w-[150px] bg-gray-50">
                          <div className="flex flex-col items-center gap-2">
                            <div className="w-8 h-8 bg-[var(--brand-blue)] rounded-lg flex items-center justify-center">
                              <Scale className="w-4 h-4 text-white" />
                            </div>
                            <div className="text-center">
                              <div className="text-sm font-semibold text-gray-900">{isRTL ? "العواقب القانونية" : "Legal/Regulatory"}</div>
                              <div className="text-xs text-gray-600">{isRTL ? "المخاطر القانونية" : "Legal Risks"}</div>
                            </div>
                          </div>
                        </th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-100">
                      {currentData.map((item, index) => (
                        <MatrixRow
                          key={item.id || index}
                          item={item}
                          index={index}
                          dpiaAssessments={dpiaAssessments}
                          onCellClick={handleCellClick}
                          isRTL={isRTL}
                        />
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </div>

            {/* Pagination Controls */}
            {totalPages > 1 && (
              <div className="px-6 py-4 bg-gray-50 border-t border-gray-100">
                <div className="flex items-center justify-between">
                  <div className="text-sm text-gray-600">
                    {isRTL
                      ? `عرض ${startIndex + 1}-${Math.min(endIndex, matrixData.length)} من ${matrixData.length}`
                      : `Showing ${startIndex + 1}-${Math.min(endIndex, matrixData.length)} of ${matrixData.length}`
                    }
                  </div>
                  <div className="flex items-center gap-3">
                    {/* Page Navigation Input */}
                    <div className="flex items-center gap-2">
                      <span className="text-sm text-gray-600">
                        {isRTL ? "الانتقال إلى:" : "Go to:"}
                      </span>
                      <input
                        type="number"
                        min="1"
                        max={totalPages}
                        value={pageInput}
                        onChange={(e) => setPageInput(e.target.value)}
                        onKeyDown={handlePageInputKeyDown}
                        placeholder={isRTL ? "رقم الصفحة" : "Page #"}
                        className="w-20 px-2 py-1 text-sm border border-gray-300 rounded focus:ring-2 focus:ring-[var(--brand-blue)]/20 focus:border-[var(--brand-blue)] outline-none text-center"
                      />
                      <Button
                        onClick={() => handlePageNavigation(pageInput)}
                        disabled={!pageInput}
                        size="sm"
                        className="bg-[var(--brand-blue)] hover:bg-[var(--brand-blue)]/90 text-white"
                      >
                        {isRTL ? "انتقال" : "Go"}
                      </Button>
                    </div>

                    {/* Previous/Next Buttons */}
                    <div className="flex items-center gap-1">
                      <Button
                        onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                        disabled={currentPage === 1}
                        variant="outline"
                        size="sm"
                        className="px-3"
                      >
                        {isRTL ? <ChevronRight className="w-4 h-4" /> : <ChevronLeft className="w-4 h-4" />}
                        {isRTL ? "السابق" : "Previous"}
                      </Button>

                      {/* Page Numbers */}
                      <div className="flex items-center gap-1 mx-2">
                        {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                          let pageNum;
                          if (totalPages <= 5) {
                            pageNum = i + 1;
                          } else if (currentPage <= 3) {
                            pageNum = i + 1;
                          } else if (currentPage >= totalPages - 2) {
                            pageNum = totalPages - 4 + i;
                          } else {
                            pageNum = currentPage - 2 + i;
                          }

                          return (
                            <Button
                              key={pageNum}
                              onClick={() => setCurrentPage(pageNum)}
                              variant={currentPage === pageNum ? "default" : "outline"}
                              size="sm"
                              className={`w-8 h-8 p-0 ${
                                currentPage === pageNum
                                  ? "bg-[var(--brand-blue)] text-white"
                                  : "text-gray-600 hover:text-gray-900"
                              }`}
                            >
                              {pageNum}
                            </Button>
                          );
                        })}
                      </div>

                      <Button
                        onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                        disabled={currentPage === totalPages}
                        variant="outline"
                        size="sm"
                        className="px-3"
                      >
                        {isRTL ? "التالي" : "Next"}
                        {isRTL ? <ChevronLeft className="w-4 h-4" /> : <ChevronRight className="w-4 h-4" />}
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Assessment Details Modal */}
      <Dialog open={showAssessmentModal} onOpenChange={setShowAssessmentModal}>
        <DialogContent className="max-w-3xl max-h-[85vh] overflow-y-auto">
          <DialogHeader className="pb-4 border-b">
            <DialogTitle className="text-lg font-semibold text-gray-900">
              {isRTL ? "تفاصيل تقييم DPIA" : "DPIA Assessment Details"}
            </DialogTitle>
            {selectedAssessment && (
              <div className="flex items-center gap-2 text-sm text-gray-600">
                <span className="font-mono bg-gray-100 px-2 py-1 rounded text-xs">
                  {selectedAssessment.tableName}.{selectedAssessment.columnName}
                </span>
                <div className={`px-2 py-1 rounded-full text-xs font-medium ${
                  selectedAssessment.finalRiskLevel === 'HIGH'
                    ? 'bg-red-100 text-red-700'
                    : selectedAssessment.finalRiskLevel === 'MEDIUM'
                    ? 'bg-yellow-100 text-yellow-700'
                    : 'bg-green-100 text-green-700'
                }`}>
                  {selectedAssessment.finalRiskLevel} RISK
                </div>
              </div>
            )}
          </DialogHeader>

          {selectedAssessment && (
            <div className="space-y-4 pt-4">
              {/* Individual Harm Domain */}
              <div className="border border-gray-200 rounded-lg">
                <div className="bg-gray-50 px-4 py-3 border-b border-gray-200">
                  <div className="flex items-center gap-2">
                    <User className="w-4 h-4 text-[var(--brand-blue)]" />
                    <h3 className="font-semibold text-gray-900">
                      {isRTL ? "الضرر الفردي" : "Individual Harm"}
                    </h3>
                  </div>
                </div>
                <div className="p-4 space-y-3">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
                    <div>
                      <span className="font-medium text-gray-700">{isRTL ? "سرقة الهوية" : "Identity Theft"}</span>
                      <p className="text-gray-600 mt-1">{selectedAssessment.domainAnswers.individualHarm.identityRecreation}</p>
                    </div>
                    <div>
                      <span className="font-medium text-gray-700">{isRTL ? "الاستغلال المالي" : "Financial Exploitation"}</span>
                      <p className="text-gray-600 mt-1">{selectedAssessment.domainAnswers.individualHarm.financialExploitation}</p>
                    </div>
                    <div>
                      <span className="font-medium text-gray-700">{isRTL ? "انتهاك الخصوصية" : "Privacy Invasion"}</span>
                      <p className="text-gray-600 mt-1">{selectedAssessment.domainAnswers.individualHarm.privacyInvasion}</p>
                    </div>
                    <div>
                      <span className="font-medium text-gray-700">{isRTL ? "التأثير النفسي" : "Psychological Impact"}</span>
                      <p className="text-gray-600 mt-1">{selectedAssessment.domainAnswers.individualHarm.psychologicalImpact}</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Business Reputation Domain */}
              <div className="border border-gray-200 rounded-lg">
                <div className="bg-gray-50 px-4 py-3 border-b border-gray-200">
                  <div className="flex items-center gap-2">
                    <Building className="w-4 h-4 text-[var(--brand-blue)]" />
                    <h3 className="font-semibold text-gray-900">
                      {isRTL ? "سمعة الأعمال" : "Business Reputation"}
                    </h3>
                  </div>
                </div>
                <div className="p-4 space-y-3">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
                    <div>
                      <span className="font-medium text-gray-700">{isRTL ? "تآكل الثقة" : "Trust Erosion"}</span>
                      <p className="text-gray-600 mt-1">{selectedAssessment.domainAnswers.businessReputation.trustErosion}</p>
                    </div>
                    <div>
                      <span className="font-medium text-gray-700">{isRTL ? "التغطية الإعلامية" : "Media Coverage"}</span>
                      <p className="text-gray-600 mt-1">{selectedAssessment.domainAnswers.businessReputation.mediaCoverage}</p>
                    </div>
                    <div>
                      <span className="font-medium text-gray-700">{isRTL ? "ضرر العلامة التجارية" : "Brand Damage"}</span>
                      <p className="text-gray-600 mt-1">{selectedAssessment.domainAnswers.businessReputation.brandDamage}</p>
                    </div>
                    <div>
                      <span className="font-medium text-gray-700">{isRTL ? "المشاكل التنافسية" : "Competitive Issues"}</span>
                      <p className="text-gray-600 mt-1">{selectedAssessment.domainAnswers.businessReputation.competitiveDisadvantage}</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Financial & Legal Domains */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Financial Consequences */}
                <div className="border border-gray-200 rounded-lg">
                  <div className="bg-gray-50 px-4 py-3 border-b border-gray-200">
                    <div className="flex items-center gap-2">
                      <DollarSign className="w-4 h-4 text-[var(--brand-blue)]" />
                      <h3 className="font-semibold text-gray-900">
                        {isRTL ? "العواقب المالية" : "Financial Consequences"}
                      </h3>
                    </div>
                  </div>
                  <div className="p-4 space-y-3 text-sm">
                    <div>
                      <span className="font-medium text-gray-700">{isRTL ? "الغرامات التنظيمية" : "Regulatory Fines"}</span>
                      <p className="text-gray-600 mt-1">{selectedAssessment.domainAnswers.financialImpact.regulatoryFines}</p>
                    </div>
                    <div>
                      <span className="font-medium text-gray-700">{isRTL ? "التكاليف القانونية" : "Legal Costs"}</span>
                      <p className="text-gray-600 mt-1">{selectedAssessment.domainAnswers.financialImpact.legalCosts}</p>
                    </div>
                  </div>
                </div>

                {/* Legal/Regulatory Consequences */}
                <div className="border border-gray-200 rounded-lg">
                  <div className="bg-gray-50 px-4 py-3 border-b border-gray-200">
                    <div className="flex items-center gap-2">
                      <Scale className="w-4 h-4 text-[var(--brand-blue)]" />
                      <h3 className="font-semibold text-gray-900">
                        {isRTL ? "العواقب القانونية" : "Legal/Regulatory"}
                      </h3>
                    </div>
                  </div>
                  <div className="p-4 space-y-3 text-sm">
                    <div>
                      <span className="font-medium text-gray-700">{isRTL ? "انتهاكات PDPL" : "PDPL Violations"}</span>
                      <p className="text-gray-600 mt-1">{selectedAssessment.domainAnswers.legalRegulatory.pdplViolations}</p>
                    </div>
                    <div>
                      <span className="font-medium text-gray-700">{isRTL ? "المسؤولية الجنائية" : "Criminal Liability"}</span>
                      <p className="text-gray-600 mt-1">{selectedAssessment.domainAnswers.legalRegulatory.criminalLiability}</p>
                    </div>
                    <div>
                      <span className="font-medium text-gray-700">{isRTL ? "القضايا الدولية" : "International Issues"}</span>
                      <p className="text-gray-600 mt-1">{selectedAssessment.domainAnswers.legalRegulatory.internationalIssues}</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

    </div>
  );
}