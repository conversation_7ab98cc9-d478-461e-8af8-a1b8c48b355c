"use client";

import React, { useState, useEffect, useCallback } from "react";
import { useParams, useRouter } from "next/navigation";
import { Locale } from "@/i18n-config";
import { ArrowLeft, ArrowRight, FileText, Plus, Eye } from "lucide-react";
import { Button } from "@/components/ui/button";
import { motion } from "framer-motion";
import { useToast } from "@/components/ui/use-toast";
import { SystemsService, System, SystemPrivacyPolicy, PolicyAnalysis } from "@/Firebase/firestore/SystemsService";
import { PrivacyPolicyModal } from "@/components/PrivacyPolicy/PrivacyPolicyModal";
import { PrivacyPolicyViewerModal } from "@/components/PrivacyPolicy/PrivacyPolicyViewerModal";
import { AnalysisDisplay } from "@/components/PrivacyPolicy/AnalysisDisplay";

export default function PrivacyPolicyPage() {
  const params = useParams();
  const router = useRouter();
  const { toast } = useToast();
  const [system, setSystem] = useState<System | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [privacyPolicies, setPrivacyPolicies] = useState<SystemPrivacyPolicy[]>([]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isViewerModalOpen, setIsViewerModalOpen] = useState(false);
  const [analysisResults, setAnalysisResults] = useState<PolicyAnalysis | null>(null);
  const [isLoadingAnalysis, setIsLoadingAnalysis] = useState(false);

  // Check if system has any privacy policy
  const hasPrivacyPolicy = privacyPolicies.length > 0;
  const hasAnalysis = analysisResults !== null;

  const lang = params?.lang as Locale;
  const systemId = params?.systemId as string;
  const isRTL = lang === "ar";

  const loadSystemDetails = useCallback(async () => {
    if (!systemId) return;

    try {
      setIsLoading(true);
      const systems = await SystemsService.getSystems();
      const foundSystem = systems.find(s => s.id === systemId);

      if (foundSystem) {
        setSystem(foundSystem);
      } else {
        toast({
          title: isRTL ? "النظام غير موجود" : "System not found",
          description: isRTL ? "لم يتم العثور على النظام المطلوب" : "The requested system was not found",
          variant: "destructive",
        });
        router.push(`/${lang}/Thiqah/DataClassification`);
      }
    } catch (error) {
      console.error('Error loading system:', error);
      toast({
        title: isRTL ? "خطأ في تحميل النظام" : "Error loading system",
        description: isRTL ? "فشل في تحميل تفاصيل النظام" : "Failed to load system details",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }, [systemId, lang, toast, router, isRTL]);

  const loadPrivacyPolicies = useCallback(async () => {
    if (!systemId) return;

    try {
      const policies = await SystemsService.getSystemPrivacyPolicies(systemId);
      setPrivacyPolicies(policies);
    } catch (error) {
      console.error('Error loading privacy policies:', error);
    }
  }, [systemId]);

  const loadAnalysis = useCallback(async () => {
    if (!systemId) return;

    try {
      setIsLoadingAnalysis(true);
      const analysis = await SystemsService.getPrivacyPolicyAnalysis(systemId);
      setAnalysisResults(analysis);
    } catch (error) {
      console.error('Error loading analysis:', error);
    } finally {
      setIsLoadingAnalysis(false);
    }
  }, [systemId]);

  useEffect(() => {
    loadSystemDetails();
    loadPrivacyPolicies();
    loadAnalysis();
  }, [loadSystemDetails, loadPrivacyPolicies, loadAnalysis]);

  const handleModalClose = () => {
    setIsModalOpen(false);
    // Reload privacy policies after modal closes
    loadPrivacyPolicies();
  };

  const handleViewerModalClose = () => {
    setIsViewerModalOpen(false);
  };

  const handleHeroButtonClick = () => {
    if (hasPrivacyPolicy) {
      setIsViewerModalOpen(true);
    } else {
      setIsModalOpen(true);
    }
  };



  if (isLoading) {
    return (
      <div className={`min-h-screen bg-gray-50 ${isRTL ? "rtl" : "ltr"}`}>
        <div className="relative min-h-screen bg-gradient-to-br from-[var(--brand-blue)] via-[var(--brand-blue)]/95 to-[var(--brand-dark-gray)] overflow-hidden">
          <div className="relative z-10 flex flex-col justify-center items-center min-h-screen px-8 py-16">
            <div className="animate-pulse text-center">
              <div className="w-24 h-24 bg-white/20 rounded-3xl mx-auto mb-8"></div>
              <div className="h-12 bg-white/20 rounded-lg w-96 mx-auto mb-4"></div>
              <div className="h-6 bg-white/20 rounded-lg w-64 mx-auto"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`min-h-screen bg-gray-50 ${isRTL ? "rtl" : "ltr"}`}>
      {/* Header */}
      <div className="relative bg-gradient-to-r from-[var(--brand-blue)] to-[var(--brand-blue)]/80">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-0 left-0 w-32 h-32 bg-white rounded-full -translate-x-1/2 -translate-y-1/2"></div>
          <div className="absolute bottom-0 right-0 w-32 h-32 bg-white rounded-full translate-x-1/2 translate-y-1/2"></div>
        </div>

        <div className="relative z-10 px-6 py-8">
          <div className="max-w-7xl mx-auto">
            {/* Back Button */}
            <motion.div
              initial={{ opacity: 0, x: isRTL ? 20 : -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5 }}
              className="mb-6"
            >
              <Button
                variant="ghost"
                onClick={() => router.back()}
                className="text-white hover:bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl px-4 py-2"
              >
                {isRTL ? (
                  <ArrowRight className="w-4 h-4 ml-2" />
                ) : (
                  <ArrowLeft className="w-4 h-4 mr-2" />
                )}
                {isRTL ? "رجوع" : "Back"}
              </Button>
            </motion.div>

            {/* Page Header */}
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center gap-6">
                <div className="w-16 h-16 bg-white/20 backdrop-blur-xl rounded-2xl flex items-center justify-center shadow-lg border border-white/20">
                  <FileText className="w-8 h-8 text-white" />
                </div>
                <div>
                  <h1 className="text-3xl md:text-4xl font-bold text-white mb-1 tracking-tight">
                    {isRTL ? "سياسة الخصوصية" : "Privacy Policy"}
                  </h1>
                  <p className="text-white/90 text-lg">
                    {system?.name}
                  </p>
                </div>
              </div>

              {/* Hero Buttons */}
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5, delay: 0.3 }}
                className="flex items-center gap-4"
              >
                <Button
                  onClick={handleHeroButtonClick}
                  className="bg-white/20 hover:bg-white/30 backdrop-blur-xl border border-white/30 text-white shadow-lg px-6 py-3 rounded-xl transition-all duration-300 hover:shadow-xl"
                >
                  {hasPrivacyPolicy ? (
                    <>
                      <Eye className="w-5 h-5 mr-2" />
                      {isRTL ? "عرض السياسة" : "View Policy"}
                    </>
                  ) : (
                    <>
                      <Plus className="w-5 h-5 mr-2" />
                      {isRTL ? "إدخال سياسة الخصوصية" : "Enter Privacy Policy"}
                    </>
                  )}
                </Button>


              </motion.div>
            </div>

            {/* Breadcrumb */}
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              className="flex items-center gap-2 text-white/70 text-sm"
            >
              <span>{isRTL ? "تصنيف البيانات" : "Data Classification"}</span>
              <span>/</span>
              <span>{system?.name}</span>
              <span>/</span>
              <span className="text-white">{isRTL ? "سياسة الخصوصية" : "Privacy Policy"}</span>
            </motion.div>
          </div>
        </div>
      </div>

      {/* Content Area */}
      <div className="px-6 py-8">
        <div className="max-w-7xl mx-auto">
          {isLoadingAnalysis ? (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.3 }}
              className="bg-white rounded-xl shadow-sm border border-gray-200 p-8"
            >
              <div className="text-center py-16">
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <div className="w-8 h-8 animate-spin rounded-full border-2 border-[var(--brand-blue)] border-t-transparent" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  {isRTL ? "جاري تحميل التحليل..." : "Loading Analysis..."}
                </h3>
                <p className="text-gray-600">
                  {isRTL ? "يرجى الانتظار" : "Please wait"}
                </p>
              </div>
            </motion.div>
          ) : hasAnalysis ? (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.3 }}
            >
              <AnalysisDisplay
                analysis={analysisResults}
                onReAnalyze={() => {}}
                isReAnalyzing={false}
                isRTL={isRTL}
              />
            </motion.div>
          ) : hasPrivacyPolicy ? (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.3 }}
              className="bg-white rounded-xl shadow-sm border border-gray-200 p-8"
            >
              <div className="text-center py-16">
                <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
                  <FileText className="w-10 h-10 text-green-600" />
                </div>
                <h3 className="text-2xl font-semibold text-gray-900 mb-3">
                  {isRTL ? "سياسة الخصوصية جاهزة" : "Privacy Policy Ready"}
                </h3>
                <p className="text-gray-600 mb-6 max-w-md mx-auto">
                  {isRTL
                    ? "تم حفظ سياسة الخصوصية بنجاح. يمكنك عرضها أو تحليلها من الأزرار أعلاه."
                    : "Your privacy policy has been saved successfully. You can view or analyze it using the buttons above."
                  }
                </p>

                {/* Policy Stats */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 max-w-lg mx-auto mt-8">
                  {privacyPolicies.map((policy) => (
                    <div key={policy.id} className="bg-gray-50 rounded-lg p-4">
                      <div className="flex items-center justify-center mb-2">
                        <span className="text-2xl">
                          {policy.language === 'ar' ? '🇸🇦' : '🇺🇸'}
                        </span>
                      </div>
                      <div className="text-sm text-gray-600 text-center">
                        <div className="font-medium text-gray-900">
                          {policy.language === 'ar' ? 'العربية' : 'English'}
                        </div>
                        <div className="mt-1">
                          {policy.content.split(' ').length} {isRTL ? 'كلمة' : 'words'}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </motion.div>
          ) : (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.3 }}
              className="bg-white rounded-xl shadow-sm border border-gray-200 p-8"
            >
              <div className="text-center py-16">
                <FileText className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  {isRTL ? "لا توجد سياسة خصوصية" : "No Privacy Policy"}
                </h3>
                <p className="text-gray-600 mb-6">
                  {isRTL
                    ? "ابدأ بإضافة سياسة الخصوصية الخاصة بنظامك من الزر أعلاه"
                    : "Start by adding your system's privacy policy using the button above"
                  }
                </p>
              </div>
            </motion.div>
          )}
        </div>
      </div>

      {/* Privacy Policy Input Modal */}
      <PrivacyPolicyModal
        isOpen={isModalOpen}
        onClose={handleModalClose}
        systemId={systemId}
        systemName={system?.name || ''}
        isRTL={isRTL}
      />

      {/* Privacy Policy Viewer Modal */}
      <PrivacyPolicyViewerModal
        isOpen={isViewerModalOpen}
        onClose={handleViewerModalClose}
        privacyPolicies={privacyPolicies}
        systemName={system?.name || ''}
        isRTL={isRTL}
      />
    </div>
  );
}
